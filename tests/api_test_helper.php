<?php
/**
 * API Test Helper
 * Simple script to test API authentication and get JW<PERSON> token
 */

// Configuration
$base_url = 'http://localhost/kvalprak';
$email = '<EMAIL>';
$password = '123';

echo "API Authentication Test\n";
echo "======================\n\n";

// Step 1: Login to get JWT token
echo "1. Logging in to get JWT token...\n";

$login_data = [
    'login_email' => $email,
    'login_password' => $password
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $base_url . '/api/auth/login');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "   HTTP Status: $http_code\n";

if ($http_code == 200) {
    $login_response = json_decode($response, true);
    
    if (isset($login_response['token'])) {
        $jwt_token = $login_response['token'];
        echo "   ✓ Login successful!\n";
        echo "   JWT Token: " . substr($jwt_token, 0, 50) . "...\n\n";
        
        // Step 2: Test API call with token
        echo "2. Testing API call with JWT token...\n";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $base_url . '/api/deviation/search');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([]));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $jwt_token
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $api_response = curl_exec($ch);
        $api_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "   HTTP Status: $api_http_code\n";
        
        if ($api_http_code == 200) {
            echo "   ✓ API call successful!\n";
            echo "   Response: " . substr($api_response, 0, 200) . "...\n";
        } else {
            echo "   ✗ API call failed\n";
            echo "   Response: $api_response\n";
        }
        
        // Step 3: Show how to use in Postman
        echo "\n3. How to use in Postman:\n";
        echo "   ========================\n";
        echo "   Method: POST\n";
        echo "   URL: $base_url/api/deviation/form/1\n";
        echo "   Headers:\n";
        echo "     Authorization: Bearer $jwt_token\n";
        echo "     Content-Type: application/json\n";
        echo "   Body (raw JSON):\n";
        echo "     {\n";
        echo "       \"your_field\": \"your_value\"\n";
        echo "     }\n\n";
        
    } else {
        echo "   ✗ Login failed - no token in response\n";
        echo "   Response: $response\n";
    }
} else {
    echo "   ✗ Login failed\n";
    echo "   Response: $response\n";
}

echo "\nFor Postman Collection:\n";
echo "======================\n";
echo "1. Create environment variable 'jwt_token'\n";
echo "2. In login request, add to Tests tab:\n";
echo "   pm.environment.set('jwt_token', pm.response.json().token);\n";
echo "3. In other requests, use Authorization header:\n";
echo "   Bearer {{jwt_token}}\n";
?>
