<?php
/**
 * Copy Structure Feature Test
 * 
 * Basic test to verify the copy structure functionality works correctly
 */

// Test configuration
$test_source_dir = '/tmp/test_source_' . time();
$test_dest_dir = '/tmp/test_dest_' . time();

echo "Copy Structure Feature Test\n";
echo "==========================\n\n";

// Create test directory structure
echo "1. Creating test directory structure...\n";
if (!mkdir($test_source_dir, 0755, true)) {
    die("Failed to create test source directory\n");
}

// Create subdirectories
mkdir($test_source_dir . '/subdir1', 0755);
mkdir($test_source_dir . '/subdir2', 0755);
mkdir($test_source_dir . '/subdir1/nested', 0755);

// Create test files
file_put_contents($test_source_dir . '/file1.txt', 'Test content 1');
file_put_contents($test_source_dir . '/file2.txt', 'Test content 2');
file_put_contents($test_source_dir . '/subdir1/file3.txt', 'Test content 3');
file_put_contents($test_source_dir . '/subdir1/nested/file4.txt', 'Test content 4');
file_put_contents($test_source_dir . '/subdir2/file5.txt', 'Test content 5');

echo "   Created test structure at: $test_source_dir\n";

// Test directory structure reading
echo "\n2. Testing directory structure reading...\n";

function get_directory_structure($path, $level = 0, $max_level = 3) {
    $structure = [];
    
    if ($level > $max_level) {
        return $structure;
    }

    if (is_dir($path)) {
        $items = scandir($path);
        foreach ($items as $item) {
            if ($item == '.' || $item == '..') {
                continue;
            }

            $item_path = $path . DIRECTORY_SEPARATOR . $item;
            $item_info = [
                'name' => $item,
                'type' => is_dir($item_path) ? 'directory' : 'file',
                'level' => $level
            ];

            if (is_dir($item_path) && $level < $max_level) {
                $item_info['children'] = get_directory_structure($item_path, $level + 1, $max_level);
            }

            $structure[] = $item_info;
        }
    }

    return $structure;
}

function display_structure($structure, $indent = '') {
    foreach ($structure as $item) {
        echo $indent . ($item['type'] === 'directory' ? '[DIR]  ' : '[FILE] ') . $item['name'] . "\n";
        if (isset($item['children']) && !empty($item['children'])) {
            display_structure($item['children'], $indent . '  ');
        }
    }
}

$structure = get_directory_structure($test_source_dir);
echo "   Directory structure:\n";
display_structure($structure, '   ');

// Test recursive copy function
echo "\n3. Testing recursive copy function...\n";

function copy_directory_recursive($source, $destination) {
    // Check if source directory exists
    if (!is_dir($source)) {
        throw new Exception("Source directory does not exist: " . $source);
    }

    // Create destination directory if it doesn't exist
    if (!is_dir($destination)) {
        if (!mkdir($destination, 0755, true)) {
            throw new Exception("Cannot create destination directory: " . $destination);
        }
    }

    // Open source directory
    $dir = opendir($source);
    if (!$dir) {
        throw new Exception("Cannot open source directory: " . $source);
    }

    // Copy each item in the directory
    while (($file = readdir($dir)) !== false) {
        // Skip current and parent directory references
        if ($file == '.' || $file == '..') {
            continue;
        }

        $source_file = $source . DIRECTORY_SEPARATOR . $file;
        $destination_file = $destination . DIRECTORY_SEPARATOR . $file;

        if (is_dir($source_file)) {
            // Recursively copy subdirectory
            copy_directory_recursive($source_file, $destination_file);
        } else {
            // Copy file
            if (!copy($source_file, $destination_file)) {
                closedir($dir);
                throw new Exception("Cannot copy file: " . $source_file . " to " . $destination_file);
            }
            
            // Preserve file permissions
            chmod($destination_file, fileperms($source_file));
        }
    }

    closedir($dir);
    return true;
}

try {
    copy_directory_recursive($test_source_dir, $test_dest_dir);
    echo "   Copy operation completed successfully\n";
    
    // Verify copy
    echo "\n4. Verifying copied structure...\n";
    $dest_structure = get_directory_structure($test_dest_dir);
    echo "   Copied directory structure:\n";
    display_structure($dest_structure, '   ');
    
    // Compare structures
    if (json_encode($structure) === json_encode($dest_structure)) {
        echo "   ✓ Structures match perfectly\n";
    } else {
        echo "   ✗ Structures do not match\n";
    }
    
    // Verify file contents
    echo "\n5. Verifying file contents...\n";
    $test_files = [
        'file1.txt' => 'Test content 1',
        'file2.txt' => 'Test content 2',
        'subdir1/file3.txt' => 'Test content 3',
        'subdir1/nested/file4.txt' => 'Test content 4',
        'subdir2/file5.txt' => 'Test content 5'
    ];
    
    $all_match = true;
    foreach ($test_files as $file => $expected_content) {
        $source_content = file_get_contents($test_source_dir . '/' . $file);
        $dest_content = file_get_contents($test_dest_dir . '/' . $file);
        
        if ($source_content === $dest_content && $dest_content === $expected_content) {
            echo "   ✓ $file content matches\n";
        } else {
            echo "   ✗ $file content does not match\n";
            $all_match = false;
        }
    }
    
    if ($all_match) {
        echo "\n✓ All tests passed successfully!\n";
    } else {
        echo "\n✗ Some tests failed\n";
    }
    
} catch (Exception $e) {
    echo "   ✗ Copy operation failed: " . $e->getMessage() . "\n";
}

// Cleanup
echo "\n6. Cleaning up test directories...\n";

function remove_directory($dir) {
    if (is_dir($dir)) {
        $objects = scandir($dir);
        foreach ($objects as $object) {
            if ($object != "." && $object != "..") {
                if (is_dir($dir . "/" . $object)) {
                    remove_directory($dir . "/" . $object);
                } else {
                    unlink($dir . "/" . $object);
                }
            }
        }
        rmdir($dir);
    }
}

remove_directory($test_source_dir);
remove_directory($test_dest_dir);

echo "   Test directories cleaned up\n";
echo "\nTest completed.\n";
