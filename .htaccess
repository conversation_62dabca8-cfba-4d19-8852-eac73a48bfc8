<IfModule mod_rewrite.c>
    RewriteEngine On    
    # Redirect everything to public_html
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ public_html/index.php/$1 [QSA,L]
</IfModule>

# Set environment variables for the application
SetEnv CI_DB_HOSTNAME localhost
SetEnv CI_DB_USERNAME admin
SetEnv CI_DB_PASSWORD 123
SetEnv CI_DB_DATABASE kvalprak
SetEnv CI_BASE_URL http://localhost
SetEnv CI_PROGRAM_NAME "KvalPrak"
SetEnv CI_PROGRAM_DESC "Quality Practice Application"
SetEnv CI_PROGRAM_DESC_SHORT "KvalPrak"
SetEnv CI_ONLY_OFFICE “true”






