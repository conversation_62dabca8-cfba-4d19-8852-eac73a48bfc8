<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Copy Structure Security Library
 * 
 * Provides security functions for the copy structure feature
 * to prevent unauthorized access and malicious operations
 */
class Copy_structure_security
{
    protected $CI;
    protected $allowed_paths = [];
    protected $excluded_dirs = [];
    protected $excluded_extensions = [];
    
    public function __construct()
    {
        $this->CI =& get_instance();
        $this->CI->load->config('copy_structure');
        
        $this->allowed_paths = $this->CI->config->item('copy_structure_allowed_paths') ?: [];
        $this->excluded_dirs = $this->CI->config->item('copy_structure_excluded_dirs') ?: [];
        $this->excluded_extensions = $this->CI->config->item('copy_structure_excluded_extensions') ?: [];
    }
    
    /**
     * Validate if a path is allowed for copy operations
     * 
     * @param string $path The path to validate
     * @return bool True if path is allowed, false otherwise
     */
    public function is_path_allowed($path)
    {
        // Normalize path
        $path = realpath($path);
        if ($path === false) {
            return false;
        }
        
        // Check against allowed paths
        foreach ($this->allowed_paths as $allowed_path) {
            $allowed_path = realpath($allowed_path);
            if ($allowed_path !== false && strpos($path, $allowed_path) === 0) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check if a directory should be excluded from operations
     * 
     * @param string $dir_name Directory name to check
     * @return bool True if directory should be excluded
     */
    public function is_directory_excluded($dir_name)
    {
        return in_array($dir_name, $this->excluded_dirs);
    }
    
    /**
     * Check if a file extension should be excluded from operations
     * 
     * @param string $filename File name to check
     * @return bool True if file should be excluded
     */
    public function is_file_excluded($filename)
    {
        $extension = pathinfo($filename, PATHINFO_EXTENSION);
        return in_array(strtolower($extension), array_map('strtolower', $this->excluded_extensions));
    }
    
    /**
     * Sanitize a path to prevent directory traversal attacks
     * 
     * @param string $path Path to sanitize
     * @return string Sanitized path
     */
    public function sanitize_path($path)
    {
        // Remove any null bytes
        $path = str_replace(chr(0), '', $path);
        
        // Remove any path traversal attempts
        $path = str_replace(['../', '..\\', '../', '..\\'], '', $path);
        
        // Normalize directory separators
        $path = str_replace('\\', '/', $path);
        
        // Remove multiple consecutive slashes
        $path = preg_replace('/\/+/', '/', $path);
        
        // Remove trailing slash
        $path = rtrim($path, '/');
        
        return $path;
    }
    
    /**
     * Validate source path for copy operation
     * 
     * @param string $path Source path to validate
     * @return array Validation result with status and message
     */
    public function validate_source_path($path)
    {
        $result = [
            'valid' => false,
            'message' => '',
            'exists' => false,
            'readable' => false
        ];
        
        // Sanitize path
        $path = $this->sanitize_path($path);
        
        // Check if path is allowed
        if (!$this->is_path_allowed($path)) {
            $result['message'] = 'Path is not in allowed directories';
            return $result;
        }
        
        // Check if path exists
        if (!file_exists($path)) {
            $result['message'] = 'Source path does not exist';
            return $result;
        }
        
        $result['exists'] = true;
        
        // Check if it's a directory
        if (!is_dir($path)) {
            $result['message'] = 'Source path is not a directory';
            return $result;
        }
        
        // Check if readable
        if (!is_readable($path)) {
            $result['message'] = 'Source directory is not readable';
            return $result;
        }
        
        $result['readable'] = true;
        $result['valid'] = true;
        $result['message'] = 'Source path is valid';
        
        return $result;
    }
    
    /**
     * Validate destination path for copy operation
     * 
     * @param string $path Destination path to validate
     * @return array Validation result with status and message
     */
    public function validate_destination_path($path)
    {
        $result = [
            'valid' => false,
            'message' => '',
            'writable' => false,
            'parent_exists' => false
        ];
        
        // Sanitize path
        $path = $this->sanitize_path($path);
        
        // Check if path is allowed
        if (!$this->is_path_allowed($path)) {
            $result['message'] = 'Path is not in allowed directories';
            return $result;
        }
        
        // Check parent directory
        $parent_dir = dirname($path);
        if (!file_exists($parent_dir)) {
            $result['message'] = 'Parent directory does not exist';
            return $result;
        }
        
        $result['parent_exists'] = true;
        
        // Check if parent is writable
        if (!is_writable($parent_dir)) {
            $result['message'] = 'Parent directory is not writable';
            return $result;
        }
        
        // If destination exists, check if it's writable
        if (file_exists($path)) {
            if (!is_writable($path)) {
                $result['message'] = 'Destination path exists but is not writable';
                return $result;
            }
        }
        
        $result['writable'] = true;
        $result['valid'] = true;
        $result['message'] = 'Destination path is valid';
        
        return $result;
    }
    
    /**
     * Log security events
     * 
     * @param string $event Event type
     * @param string $message Event message
     * @param array $context Additional context
     */
    public function log_security_event($event, $message, $context = [])
    {
        if ($this->CI->config->item('copy_structure_enable_logging')) {
            $log_entry = [
                'timestamp' => date('Y-m-d H:i:s'),
                'event' => $event,
                'message' => $message,
                'user_id' => $this->CI->session->userdata('user_id') ?: 'unknown',
                'ip_address' => $this->CI->input->ip_address(),
                'user_agent' => $this->CI->input->user_agent(),
                'context' => $context
            ];
            
            $log_file = $this->CI->config->item('copy_structure_log_file');
            if ($log_file) {
                $log_line = json_encode($log_entry) . "\n";
                file_put_contents($log_file, $log_line, FILE_APPEND | LOCK_EX);
            }
        }
    }
    
    /**
     * Check if user has permission to perform copy operations
     * 
     * @return bool True if user has permission
     */
    public function has_copy_permission()
    {
        // Check if user is logged in
        if (!$this->CI->session->userdata('logged_in')) {
            return false;
        }
        
        // Check if user has admin role (assuming is_role function exists)
        if (function_exists('is_role')) {
            return is_role('Systemadministratör');
        }
        
        // Fallback: check if user is in admin controller context
        return true; // This should be customized based on your auth system
    }
}
