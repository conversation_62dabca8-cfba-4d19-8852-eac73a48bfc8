<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Copy_menue extends Admin_Controller
{

	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
		$this->load->helper(['file', 'directory']);
	}

	/**
	 * عرض صفحة نسخ الهيكل
	 */
	public function index()
	{
		$this->data['title'] = lang('copy_structure');
		$this->load->view('admin/copy_menue/index', $this->data);
	}

	/**
	 * نسخ الهيكل الكامل - المجلدات والملفات
	 */
	public function copy_structure()
	{
		$this->load->helper('form');
		$this->load->library('form_validation');

		$validation_rules = [
			[
				'field' => 'source_path',
				'label' => lang('source_path'),
				'rules' => ['required', 'trim']
			],
			[
				'field' => 'destination_path', 
				'label' => lang('destination_path'),
				'rules' => ['required', 'trim']
			]
		];

		$this->form_validation->set_rules($validation_rules);

		if ($this->form_validation->run() === TRUE) {
			$source = $this->input->post('source_path');
			$destination = $this->input->post('destination_path');

			try {
				$result = $this->_copy_directory_recursive($source, $destination);
				
				if ($result) {
					$this->session->set_flashdata('success', lang('structure_copied_successfully'));
				} else {
					$this->session->set_flashdata('error', lang('structure_copy_failed'));
				}
			} catch (Exception $e) {
				$this->session->set_flashdata('error', lang('error') . ': ' . $e->getMessage());
			}

			redirect('admin/copy_menue');
		} else {
			$this->data['title'] = lang('copy_structure');
			$this->load->view('admin/copy_menue/index', $this->data);
		}
	}

	/**
	 * نسخ المجلد بشكل تكراري مع كامل محتوياته
	 */
	private function _copy_directory_recursive($source, $destination)
	{
		// التأكد من وجود المجلد المصدر
		if (!is_dir($source)) {
			throw new Exception("Source directory does not exist: " . $source);
		}

		// إنشاء المجلد الهدف إذا لم يكن موجوداً
		if (!is_dir($destination)) {
			if (!mkdir($destination, 0755, true)) {
				throw new Exception("Cannot create destination directory: " . $destination);
			}
		}

		// فتح المجلد المصدر
		$dir = opendir($source);
		if (!$dir) {
			throw new Exception("Cannot open source directory: " . $source);
		}

		// نسخ كل عنصر في المجلد
		while (($file = readdir($dir)) !== false) {
			// تجاهل المجلدات الحالية والسابقة
			if ($file == '.' || $file == '..') {
				continue;
			}

			$source_file = $source . DIRECTORY_SEPARATOR . $file;
			$destination_file = $destination . DIRECTORY_SEPARATOR . $file;

			if (is_dir($source_file)) {
				// نسخ المجلد الفرعي تكرارياً
				$this->_copy_directory_recursive($source_file, $destination_file);
			} else {
				// نسخ الملف
				if (!copy($source_file, $destination_file)) {
					closedir($dir);
					throw new Exception("Cannot copy file: " . $source_file . " to " . $destination_file);
				}
				
				// الحفاظ على صلاحيات الملف
				chmod($destination_file, fileperms($source_file));
			}
		}

		closedir($dir);
		return true;
	}

	/**
	 * الحصول على قائمة المجلدات المتاحة
	 */
	public function get_directories()
	{
		$base_path = $this->input->post('base_path') ?: APPPATH;
		
		$directories = [];
		if (is_dir($base_path)) {
			$dirs = scandir($base_path);
			foreach ($dirs as $dir) {
				if ($dir != '.' && $dir != '..' && is_dir($base_path . DIRECTORY_SEPARATOR . $dir)) {
					$directories[] = [
						'name' => $dir,
						'path' => $base_path . DIRECTORY_SEPARATOR . $dir
					];
				}
			}
		}

		$this->output
			->set_content_type('application/json')
			->set_output(json_encode($directories));
	}

	/**
	 * معاينة الهيكل قبل النسخ
	 */
	public function preview_structure()
	{
		$source_path = $this->input->post('source_path');
		
		if (!$source_path || !is_dir($source_path)) {
			$this->output
				->set_content_type('application/json')
				->set_output(json_encode(['error' => lang('invalid_source_path')]));
			return;
		}

		$structure = $this->_get_directory_structure($source_path);
		
		$this->output
			->set_content_type('application/json')
			->set_output(json_encode($structure));
	}

	/**
	 * الحصول على هيكل المجلد
	 */
	private function _get_directory_structure($path, $level = 0, $max_level = 3)
	{
		$structure = [];
		
		if ($level > $max_level) {
			return $structure;
		}

		if (is_dir($path)) {
			$items = scandir($path);
			foreach ($items as $item) {
				if ($item == '.' || $item == '..') {
					continue;
				}

				$item_path = $path . DIRECTORY_SEPARATOR . $item;
				$item_info = [
					'name' => $item,
					'type' => is_dir($item_path) ? 'directory' : 'file',
					'level' => $level
				];

				if (is_dir($item_path) && $level < $max_level) {
					$item_info['children'] = $this->_get_directory_structure($item_path, $level + 1, $max_level);
				}

				$structure[] = $item_info;
			}
		}

		return $structure;
	}

	/**
	 * التحقق من صحة المسار
	 */
	public function validate_path()
	{
		$path = $this->input->post('path');
		$is_valid = is_dir($path);
		$is_writable = is_writable(dirname($path));
		
		$this->output
			->set_content_type('application/json')
			->set_output(json_encode([
				'valid' => $is_valid,
				'writable' => $is_writable,
				'exists' => file_exists($path)
			]));
	}
}
