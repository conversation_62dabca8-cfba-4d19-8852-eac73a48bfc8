<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Copy_menue extends Admin_Controller
{

	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
		$this->load->helper(['file', 'directory']);
		$this->load->model(['menu_model', 'document_model', 'folder_model']);
	}

	/**
	 * Show the structure copy page
	 */
	public function index()
	{
		$this->load->helper('form');
		$this->data['title'] = lang('copy_structure');
		$this->data['menus'] = $this->menu_model->get_all();
		$this->load->view('admin/copy_menue/index', $this->data);
	}

	/**
	 *Copy the full structure - folders and files
	 */
	public function copy_structure()
	{
		$this->load->helper('form');
		$this->load->library('form_validation');

		$validation_rules = [
			[
				'field' => 'source_path',
				'label' => lang('source_path'),
				'rules' => ['required', 'trim']
			],
			[
				'field' => 'destination_path', 
				'label' => lang('destination_path'),
				'rules' => ['required', 'trim']
			]
		];

		$this->form_validation->set_rules($validation_rules);

		if ($this->form_validation->run() === TRUE) {
			$source = $this->input->post('source_path');
			$destination = $this->input->post('destination_path');

			try {
				$result = $this->_copy_directory_recursive($source, $destination);
				
				if ($result) {
					$this->session->set_flashdata('success', lang('structure_copied_successfully'));
				} else {
					$this->session->set_flashdata('error', lang('structure_copy_failed'));
				}
			} catch (Exception $e) {
				$this->session->set_flashdata('error', lang('error') . ': ' . $e->getMessage());
			}

			redirect('admin/copy_menue');
		} else {
			$this->data['title'] = lang('copy_structure');
			$this->load->view('admin/copy_menue/index', $this->data);
		}
	}

	/**
	 *Recursively copy the folder with all its contents.
	 */
	private function _copy_directory_recursive($source, $destination)
	{
		// Ensure the source folder exists.
		if (!is_dir($source)) {
			throw new Exception("Source directory does not exist: " . $source);
		}

		// Create the destination folder if it doesn't exist
		if (!is_dir($destination)) {
			if (!mkdir($destination, 0755, true)) {
				throw new Exception("Cannot create destination directory: " . $destination);
			}
		}

		// Open the source directory
		$dir = opendir($source);
		if (!$dir) {
			throw new Exception("Cannot open source directory: " . $source);
		}

		// Copy each item in the folder
		while (($file = readdir($dir)) !== false) {
			// Ignore current and parent directories
			if ($file == '.' || $file == '..') {
				continue;
			}

			$source_file = $source . DIRECTORY_SEPARATOR . $file;
			$destination_file = $destination . DIRECTORY_SEPARATOR . $file;

			if (is_dir($source_file)) {
				$this->_copy_directory_recursive($source_file, $destination_file);
			} else {
				if (!copy($source_file, $destination_file)) {
					closedir($dir);
					throw new Exception("Cannot copy file: " . $source_file . " to " . $destination_file);
				}
				
				//  Maintain file permissions
				chmod($destination_file, fileperms($source_file));
			}
		}

		closedir($dir);
		return true;
	}


	public function get_directories()
	{
		$base_path = $this->input->post('base_path') ?: APPPATH;
		
		$directories = [];
		if (is_dir($base_path)) {
			$dirs = scandir($base_path);
			foreach ($dirs as $dir) {
				if ($dir != '.' && $dir != '..' && is_dir($base_path . DIRECTORY_SEPARATOR . $dir)) {
					$directories[] = [
						'name' => $dir,
						'path' => $base_path . DIRECTORY_SEPARATOR . $dir
					];
				}
			}
		}

		$this->output
			->set_content_type('application/json')
			->set_output(json_encode($directories));
	}

	public function preview_structure()
	{
		$source_path = $this->input->post('source_path');
		
		if (!$source_path || !is_dir($source_path)) {
			$this->output
				->set_content_type('application/json')
				->set_output(json_encode(['error' => lang('invalid_source_path')]));
			return;
		}

		$structure = $this->_get_directory_structure($source_path);
		
		$this->output
			->set_content_type('application/json')
			->set_output(json_encode($structure));
	}

	/**
	 *Get folder structure
	 */
	private function _get_directory_structure($path, $level = 0, $max_level = 3)
	{
		$structure = [];

		if ($level > $max_level) {
			return $structure;
		}

		if (is_dir($path)) {
			$items = scandir($path);
			foreach ($items as $item) {
				if ($item == '.' || $item == '..') {
					continue;
				}

				$item_path = $path . DIRECTORY_SEPARATOR . $item;
				$item_info = [
					'name' => $item,
					'type' => is_dir($item_path) ? 'directory' : 'file',
					'level' => $level
				];

				if (is_dir($item_path) && $level < $max_level) {
					$item_info['children'] = $this->_get_directory_structure($item_path, $level + 1, $max_level);
				}

				$structure[] = $item_info;
			}
		}

		return $structure;
	}

	/**
	 * Path validation
	 */
	public function validate_path()
	{
		$source_path = $this->input->post('source_path');
		$destination_path = $this->input->post('destination_path');

		$source_valid = is_dir($source_path);
		$source_exists = file_exists($source_path);
		$destination_writable = is_writable(dirname($destination_path)) || is_writable($destination_path);

		$this->output
			->set_content_type('application/json')
			->set_output(json_encode([
				'source_valid' => $source_valid,
				'source_exists' => $source_exists,
				'destination_writable' => $destination_writable
			]));
	}

	/**
	 * Copy complete document structure from system
	 */
	public function copy_document_structure()
	{
		$this->load->helper('form');
		$this->load->library('form_validation');

		$validation_rules = [
			[
				'field' => 'source_menu_id',
				'label' => lang('source_menu'),
				'rules' => ['required', 'trim']
			],
			[
				'field' => 'destination_name',
				'label' => lang('destination_name'),
				'rules' => ['required', 'trim']
			]
		];

		$this->form_validation->set_rules($validation_rules);

		if ($this->form_validation->run() === TRUE) {
			$source_menu_id = $this->input->post('source_menu_id');
			$destination_name = $this->input->post('destination_name');
			$copy_files = $this->input->post('copy_files') == '1';

			try {
				$this->db->trans_start();
				
				// Copy menu hierarchy structure
				$new_structure = $this->_copy_menu_hierarchy($source_menu_id, $destination_name);
				
				// Copy all documents in structure
				$this->_copy_all_documents($source_menu_id, $new_structure, $copy_files);
				
				$this->db->trans_complete();
				
				if ($this->db->trans_status() === FALSE) {
					throw new Exception("Database transaction failed");
				}
				
				$this->session->set_flashdata('success', lang('document_structure_copied_successfully'));
				
			} catch (Exception $e) {
				$this->db->trans_rollback();
				$this->session->set_flashdata('error', lang('error') . ': ' . $e->getMessage());
			}

			redirect('admin/copy_menue/document_structure');
		} else {
			$this->data['title'] = lang('copy_document_structure');
			$this->data['menus'] = $this->menu_model->get_all();
			$this->load->view('admin/copy_menue/document_structure', $this->data);
		}
	}

	/**
	 * Show document structure copy page
	 */
	public function document_structure()
	{
		$this->load->helper('form');
		$this->data['title'] = lang('copy_document_structure');
		$this->data['menus'] = $this->menu_model->get_all();
		$this->load->view('admin/copy_menue/document_structure', $this->data);
	}

	/**
	 * Copy menu hierarchy structure recursively
	 */
	private function _copy_menu_hierarchy($source_menu_id, $destination_name)
	{
		// Get original menu
		$source_menu = $this->menu_model->get($source_menu_id);
		if (!$source_menu) {
			throw new Exception("Source menu not found");
		}
		
		// Create new main menu
		$new_menu_id = UUID_TO_BIN(UUIDv4());
		$new_menu_data = [
			'menu_id' => $new_menu_id,
			'name' => $destination_name,
			'parent_id' => $source_menu->parent_id,
			'type' => $source_menu->type,
			'company_id' => $this->auth_company_id,
			'sort_order' => $this->menu_model->get_next_sort_order($source_menu->parent_id),
			'created_date' => date('Y-m-d H:i:s'),
			'created_by' => $this->auth_user_id
		];
		
		$this->menu_model->save($new_menu_data);
		
		// Copy all sub-folders maintaining hierarchy
		$folder_mapping = $this->_copy_folder_tree($source_menu_id, BIN_TO_UUID($new_menu_id));
		$folder_mapping[BIN_TO_UUID($source_menu->menu_id)] = BIN_TO_UUID($new_menu_id);
		
		return [
			'new_menu_id' => BIN_TO_UUID($new_menu_id),
			'folder_mapping' => $folder_mapping
		];
	}

	/**
	 * Copy folder tree recursively
	 */
	private function _copy_folder_tree($parent_id, $new_parent_id, $level = 0)
	{
		$folder_mapping = [];
		
		if ($level > 10) { // Prevent infinite recursion
			return $folder_mapping;
		}
		
		// Get sub-folders
		$folders = $this->menu_model->get_children($parent_id);
		
		foreach ($folders as $folder) {
			$new_folder_id = UUIDv4();
			$folder_mapping[BIN_TO_UUID($folder->menu_id)] = $new_folder_id;
			
			// Copy folder
			$new_folder_data = [
				'menu_id' => UUID_TO_BIN($new_folder_id),
				'name' => $folder->name,
				'parent_id' => UUID_TO_BIN($new_parent_id),
				'type' => $folder->type,
				'company_id' => $this->auth_company_id,
				'sort_order' => $folder->sort_order,
				'created_date' => date('Y-m-d H:i:s'),
				'created_by' => $this->auth_user_id
			];
			
			$this->menu_model->save($new_folder_data);
			
			// Copy folder permissions
			$this->_copy_folder_permissions(BIN_TO_UUID($folder->menu_id), $new_folder_id);
			
			// Copy sub-folders recursively
			$sub_folders = $this->_copy_folder_tree(BIN_TO_UUID($folder->menu_id), $new_folder_id, $level + 1);
			$folder_mapping = array_merge($folder_mapping, $sub_folders);
		}
		
		return $folder_mapping;
	}

	/**
	 * Copy all documents in structure
	 */
	private function _copy_all_documents($source_menu_id, $new_structure, $copy_physical_files = false)
	{
		$folder_mapping = $new_structure['folder_mapping'];
		
		// Get all documents in the structure tree
		$documents = $this->document_model->get_all_by_menu_tree($source_menu_id);
		
		foreach ($documents as $document) {
			$new_document_id = UUIDv4();
			
			// Determine new folder
			$new_folder_id = isset($folder_mapping[BIN_TO_UUID($document->folder_id)]) 
				? $folder_mapping[BIN_TO_UUID($document->folder_id)] 
				: $new_structure['new_menu_id'];
			
			// Copy document data
			$new_document_data = [
				'document_id' => UUID_TO_BIN($new_document_id),
				'title' => $document->title . ' (Copy)',
				'description' => $document->description,
				'folder_id' => UUID_TO_BIN($new_folder_id),
				'document_type' => $document->document_type,
				'status' => 'draft',
				'version' => 1,
				'created_date' => date('Y-m-d H:i:s'),
				'created_by' => $this->auth_user_id,
				'company_id' => $this->auth_company_id
			];
			
			$this->document_model->save($new_document_data);
			
			// Copy attachments
			if ($copy_physical_files) {
				$this->_copy_document_files(BIN_TO_UUID($document->document_id), $new_document_id);
			}
			
			// Copy permissions and relationships
			$this->_copy_document_relationships(BIN_TO_UUID($document->document_id), $new_document_id);
		}
	}

	/**
	 * Copy folder permissions
	 */
	private function _copy_folder_permissions($source_folder_id, $new_folder_id)
	{
		// Copy menu group relationships
		$menu_groups = $this->db->get_where('menu_group', ['menu_id' => UUID_TO_BIN($source_folder_id)])->result();
		
		foreach ($menu_groups as $menu_group) {
			$new_menu_group_data = [
				'menu_id' => UUID_TO_BIN($new_folder_id),
				'group_id' => $menu_group->group_id
			];
			
			$this->db->insert('menu_group', $new_menu_group_data);
		}
	}

	/**
	 * Copy document files and attachments
	 */
	private function _copy_document_files($source_document_id, $new_document_id)
	{
		$attachments = $this->document_model->get_attachments($source_document_id);
		
		foreach ($attachments as $attachment) {
			$new_attachment_id = UUIDv4();
			
			// Copy attachment record
			$new_attachment_data = [
				'attachment_id' => UUID_TO_BIN($new_attachment_id),
				'document_id' => UUID_TO_BIN($new_document_id),
				'file_name' => $attachment->file_name,
				'original_name' => $attachment->original_name,
				'file_size' => $attachment->file_size,
				'mime_type' => $attachment->mime_type,
				'file_path' => $attachment->file_path,
				'uploaded_date' => date('Y-m-d H:i:s'),
				'uploaded_by' => $this->auth_user_id
			];
			
			$this->document_model->save_attachment($new_attachment_data);
			
			// Copy physical file if exists
			$source_file = FCPATH . $attachment->file_path;
			if (file_exists($source_file)) {
				$new_file_path = str_replace($source_document_id, $new_document_id, $attachment->file_path);
				$dest_file = FCPATH . $new_file_path;
				
				// Create directory if not exists
				$dest_dir = dirname($dest_file);
				if (!is_dir($dest_dir)) {
					mkdir($dest_dir, 0755, true);
				}
				
				copy($source_file, $dest_file);
				chmod($dest_file, fileperms($source_file));
			}
		}
	}

	/**
	 * Copy document relationships
	 */
	private function _copy_document_relationships($source_document_id, $new_document_id)
	{
		// Copy document groups
		$document_groups = $this->db->get_where('document_group', ['document_id' => UUID_TO_BIN($source_document_id)])->result();
		
		foreach ($document_groups as $doc_group) {
			$new_doc_group_data = [
				'document_id' => UUID_TO_BIN($new_document_id),
				'group_id' => $doc_group->group_id
			];
			
			$this->db->insert('document_group', $new_doc_group_data);
		}
		
		// Copy document editors
		$document_editors = $this->db->get_where('document_editor', ['document_id' => UUID_TO_BIN($source_document_id)])->result();
		
		foreach ($document_editors as $editor) {
			$new_editor_data = [
				'document_id' => UUID_TO_BIN($new_document_id),
				'user_id' => $editor->user_id
			];
			
			$this->db->insert('document_editor', $new_editor_data);
		}
	}

	/**
	 * Preview menu structure for AJAX
	 */
	public function preview_menu_structure()
	{
		$menu_id = $this->input->post('menu_id');
		
		if (!$menu_id) {
			$this->output
				->set_content_type('application/json')
				->set_output(json_encode(['error' => lang('invalid_menu_id')]));
			return;
		}

		try {
			$structure = $this->_get_menu_structure($menu_id);
			
			$this->output
				->set_content_type('application/json')
				->set_output(json_encode($structure));
		} catch (Exception $e) {
			$this->output
				->set_content_type('application/json')
				->set_output(json_encode(['error' => $e->getMessage()]));
		}
	}

	/**
	 * Get menu statistics for AJAX
	 */
	public function get_menu_statistics()
	{
		$menu_id = $this->input->post('menu_id');
		
		if (!$menu_id) {
			$this->output
				->set_content_type('application/json')
				->set_output(json_encode(['error' => lang('invalid_menu_id')]));
			return;
		}

		try {
			$stats = $this->_get_menu_statistics($menu_id);
			
			$this->output
				->set_content_type('application/json')
				->set_output(json_encode($stats));
		} catch (Exception $e) {
			$this->output
				->set_content_type('application/json')
				->set_output(json_encode(['error' => $e->getMessage()]));
		}
	}

	/**
	 * Get menu structure tree
	 */
	private function _get_menu_structure($menu_id, $level = 0)
	{
		$structure = [];
		
		if ($level > 5) { // Prevent deep recursion
			return $structure;
		}
		
		// Get menu/folder info
		$menu = $this->menu_model->get($menu_id);
		if (!$menu) {
			return $structure;
		}
		
		// Get sub-folders
		$children = $this->menu_model->get_children($menu_id);
		
		foreach ($children as $child) {
			$child_structure = [
				'name' => $child->name,
				'type' => $child->type,
				'level' => $level,
				'document_count' => $this->document_model->count_documents_in_folder(BIN_TO_UUID($child->menu_id))
			];
			
			// Get sub-structure recursively
			$child_structure['children'] = $this->_get_menu_structure(BIN_TO_UUID($child->menu_id), $level + 1);
			
			$structure[] = $child_structure;
		}
		
		return $structure;
	}

	/**
	 * Get menu statistics
	 */
	private function _get_menu_statistics($menu_id)
	{
		$folders = $this->menu_model->count_folders_in_tree($menu_id);
		$documents = $this->document_model->count_documents_in_tree($menu_id);
		$attachments = $this->document_model->count_attachments_in_tree($menu_id);
		$total_size = $this->document_model->get_total_size_in_tree($menu_id);
		
		return [
			'folders' => $folders,
			'documents' => $documents,
			'attachments' => $attachments,
			'total_size' => $total_size ?: 0
		];
	}
}
