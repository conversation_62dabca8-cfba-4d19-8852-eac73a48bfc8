<?php $this->load->view('template/header'); ?>
<link rel="stylesheet" href="<?php echo base_url('assets/css/admin/copy-structure.css'); ?>">
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-copy-structure',
					'class' => 'btn btn-primary',
					'title' => lang('copy_now'),
					'content' => '<i class="fa fa-copy"></i> ' . lang('copy_now')
				));
				
				echo form_button(array(
					'type' => 'button',
					'id' => 'btn-preview',
					'class' => 'btn btn-info',
					'title' => lang('preview_structure'),
					'content' => '<i class="fa fa-eye"></i> ' . lang('preview_structure')
				));
				
				echo form_button(array(
					'type' => 'button',
					'id' => 'btn-validate',
					'class' => 'btn btn-warning',
					'title' => lang('validate_paths'),
					'content' => '<i class="fa fa-check"></i> ' . lang('validate_paths')
				));
				?>
			</div>
			<h1>
				<?php echo lang('copy_structure'); ?>
				<small><?php echo lang('copy_structure_desc'); ?></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<?php if($this->session->flashdata('success')): ?>
						<div class="alert alert-success alert-dismissible">
							<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
							<i class="icon fa fa-check"></i> <?php echo $this->session->flashdata('success'); ?>
						</div>
					<?php endif; ?>
					
					<?php if($this->session->flashdata('error')): ?>
						<div class="alert alert-danger alert-dismissible">
							<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
							<i class="icon fa fa-ban"></i> <?php echo $this->session->flashdata('error'); ?>
						</div>
					<?php endif; ?>
					
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('copy_structure'); ?></h3>
						</div>
						<div class="box-body">
							<p><?php echo lang('copy_structure_desc'); ?></p>
							<?php 
								echo validation_errors('<div class="alert alert-danger">', '</div>');
								echo form_open('admin/copy_menue/copy_structure', array(
									'id' => 'form-copy-structure',
									'autocomplete' => 'off'
								));
							?>
							
							<div class="row">
								<div class="col-md-6">
									<div class="form-group">
										<?php echo form_label(lang('source_path'), 'source_path'); ?>
										<div class="input-group">
											<?php echo form_input(array(
												'name' => 'source_path',
												'id' => 'source_path',
												'class' => 'form-control',
												'placeholder' => lang('source_path_help'),
												'value' => set_value('source_path')
											)); ?>
											<div class="input-group-btn">
												<button type="button" class="btn btn-default" id="btn-browse-source">
													<i class="fa fa-folder-open"></i> <?php echo lang('browse_directories'); ?>
												</button>
											</div>
										</div>
										<small class="form-text text-muted"><?php echo lang('source_path_help'); ?></small>
									</div>
								</div>
								
								<div class="col-md-6">
									<div class="form-group">
										<?php echo form_label(lang('destination_path'), 'destination_path'); ?>
										<div class="input-group">
											<?php echo form_input(array(
												'name' => 'destination_path',
												'id' => 'destination_path',
												'class' => 'form-control',
												'placeholder' => lang('destination_path_help'),
												'value' => set_value('destination_path')
											)); ?>
											<div class="input-group-btn">
												<button type="button" class="btn btn-default" id="btn-browse-destination">
													<i class="fa fa-folder-open"></i> <?php echo lang('browse_directories'); ?>
												</button>
											</div>
										</div>
										<small class="form-text text-muted"><?php echo lang('destination_path_help'); ?></small>
									</div>
								</div>
							</div>
							
							<div class="row">
								<div class="col-md-12">
									<div class="panel panel-default" id="validation-panel" style="display: none;">
										<div class="panel-header">
											<h4><?php echo lang('path_validation'); ?></h4>
										</div>
										<div class="panel-body" id="validation-results">
											<!-- Validation results will be displayed here -->
										</div>
									</div>
								</div>
							</div>
							
							<div class="row">
								<div class="col-md-12">
									<div class="panel panel-info" id="preview-panel" style="display: none;">
										<div class="panel-header">
											<h4><?php echo lang('preview_structure'); ?></h4>
										</div>
										<div class="panel-body" id="preview-results">
											<!-- Preview results will be displayed here -->
										</div>
									</div>
								</div>
							</div>
							
							<hr/>
							
							<div class="row">
								<div class="col-md-12">
									<h4><?php echo lang('advanced_options'); ?></h4>
									
									<div class="form-group">
										<div class="checkbox">
											<label>
												<?php echo form_checkbox('preserve_permissions', '1', FALSE); ?>
												<?php echo lang('preserve_permissions'); ?>
											</label>
										</div>
									</div>
									
									<div class="form-group">
										<div class="checkbox">
											<label>
												<?php echo form_checkbox('preserve_timestamps', '1', FALSE); ?>
												<?php echo lang('preserve_timestamps'); ?>
											</label>
										</div>
									</div>
									
									<div class="form-group">
										<div class="checkbox">
											<label>
												<?php echo form_checkbox('exclude_hidden', '1', TRUE); ?>
												<?php echo lang('exclude_hidden'); ?>
											</label>
										</div>
									</div>
									
									<div class="form-group">
										<div class="checkbox">
											<label>
												<?php echo form_checkbox('overwrite_existing', '1', FALSE); ?>
												<?php echo lang('overwrite_existing'); ?>
											</label>
										</div>
										<small class="form-text text-warning"><?php echo lang('overwrite_warning'); ?></small>
									</div>
								</div>
							</div>
							
							<hr/>
							
							<div class="form-group">
								<?php echo form_label(lang('confirm_copy'), 'confirm_copy'); ?>
								<div class="checkbox">
									<label>
										<?php echo form_checkbox('confirm_copy', '1', FALSE, 'required'); ?>
										<?php echo lang('confirm_copy_help'); ?>
									</label>
								</div>
							</div>
							
							<?php echo form_close(); ?>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
	</div>
	<!-- /.content-wrapper -->

<!-- Directory Browser Modal -->
<div class="modal fade" id="directory-modal" tabindex="-1" role="dialog">
	<div class="modal-dialog modal-lg" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
				<h4 class="modal-title"><?php echo lang('browse_directories'); ?></h4>
			</div>
			<div class="modal-body">
				<div id="directory-tree">
					<!-- Directory tree will be loaded here -->
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal"><?php echo lang('cancel'); ?></button>
				<button type="button" class="btn btn-primary" id="select-directory"><?php echo lang('select_source'); ?></button>
			</div>
		</div>
	</div>
</div>

<script src="<?php echo base_url('assets/js/admin/copy-structure.js'); ?>"></script>
<script>
// Set language-specific text for JavaScript
$(document).ready(function() {
	$('#select-directory').data('source-text', '<?php echo lang('select_source'); ?>');
	$('#select-directory').data('destination-text', '<?php echo lang('select_destination'); ?>');
});
</script>



<?php $this->load->view('template/footer'); ?>
