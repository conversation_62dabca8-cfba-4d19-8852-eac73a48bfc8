<?php $this->load->view('template/header'); ?>

<div class="content-wrapper">
	<section class="content-header">
		<h1>
			<?php echo lang('copy_structure'); ?>
			<small><?php echo lang('copy_structure_desc'); ?></small>
		</h1>
		<ol class="breadcrumb">
			<li><a href="<?php echo site_url('admin'); ?>"><i class="fa fa-dashboard"></i> <?php echo lang('home'); ?></a></li>
			<li class="active"><?php echo lang('copy_structure'); ?></li>
		</ol>
	</section>

	<section class="content">
		<!-- Flash messages -->
		<?php if ($this->session->flashdata('success')): ?>
			<div class="alert alert-success alert-dismissible">
				<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
				<i class="icon fa fa-check"></i> <?php echo $this->session->flashdata('success'); ?>
			</div>
		<?php endif; ?>

		<?php if ($this->session->flashdata('error')): ?>
			<div class="alert alert-danger alert-dismissible">
				<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
				<i class="icon fa fa-ban"></i> <?php echo $this->session->flashdata('error'); ?>
			</div>
		<?php endif; ?>

		<div class="row">
			<div class="col-md-6">
				<!-- Physical File Copy -->
				<div class="box box-primary">
					<div class="box-header with-border">
						<h3 class="box-title"><i class="fa fa-folder"></i> <?php echo lang('copy_physical_structure'); ?></h3>
					</div>
					
					<?php echo form_open('admin/copy_menue/copy_structure', ['class' => 'form-horizontal']); ?>
					<div class="box-body">
						<div class="form-group">
							<label class="col-sm-4 control-label"><?php echo lang('source_path'); ?></label>
							<div class="col-sm-8">
								<?php echo form_input([
									'name' => 'source_path',
									'class' => 'form-control',
									'placeholder' => '/var/www/html/source',
									'value' => set_value('source_path')
								]); ?>
								<?php echo form_error('source_path', '<div class="text-red">', '</div>'); ?>
							</div>
						</div>

						<div class="form-group">
							<label class="col-sm-4 control-label"><?php echo lang('destination_path'); ?></label>
							<div class="col-sm-8">
								<?php echo form_input([
									'name' => 'destination_path',
									'class' => 'form-control',
									'placeholder' => '/var/www/html/destination',
									'value' => set_value('destination_path')
								]); ?>
								<?php echo form_error('destination_path', '<div class="text-red">', '</div>'); ?>
							</div>
						</div>
					</div>
					
					<div class="box-footer">
						<div class="col-sm-offset-4 col-sm-8">
							<button type="button" class="btn btn-info btn-sm" onclick="validatePaths()">
								<i class="fa fa-check"></i> <?php echo lang('validate_paths'); ?>
							</button>
							<button type="button" class="btn btn-info btn-sm" onclick="previewStructure()">
								<i class="fa fa-eye"></i> <?php echo lang('preview_structure'); ?>
							</button>
							<button type="submit" class="btn btn-primary">
								<i class="fa fa-copy"></i> <?php echo lang('copy_structure'); ?>
							</button>
						</div>
					</div>
					<?php echo form_close(); ?>
				</div>
			</div>

			<div class="col-md-6">
				<!-- Document Structure Copy -->
				<div class="box box-success">
					<div class="box-header with-border">
						<h3 class="box-title"><i class="fa fa-file-text"></i> <?php echo lang('copy_document_structure'); ?></h3>
					</div>
					
					<div class="box-body">
						<p class="text-muted"><?php echo lang('copy_document_structure_desc'); ?></p>
						<div class="form-group">
							<a href="<?php echo site_url('admin/copy_menue/document_structure'); ?>" class="btn btn-success btn-block">
								<i class="fa fa-copy"></i> <?php echo lang('copy_document_structure'); ?>
							</a>
						</div>
					</div>
				</div>

				<!-- Preview Panel -->
				<div class="box box-info">
					<div class="box-header with-border">
						<h3 class="box-title"><i class="fa fa-tree"></i> <?php echo lang('structure_preview'); ?></h3>
					</div>
					<div class="box-body">
						<div id="structure-preview">
							<p class="text-muted"><?php echo lang('select_source_to_preview'); ?></p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
</div>

<script>
function validatePaths() {
	const sourcePath = $('input[name="source_path"]').val();
	const destinationPath = $('input[name="destination_path"]').val();
	
	if (!sourcePath || !destinationPath) {
		alert('<?php echo lang('please_enter_both_paths'); ?>');
		return;
	}

	$.post('<?php echo site_url('admin/copy_menue/validate_path'); ?>', {
		source_path: sourcePath,
		destination_path: destinationPath
	}, function(response) {
		let message = '';
		if (response.source_valid && response.source_exists) {
			message += '✓ <?php echo lang('source_path_valid'); ?>\n';
		} else {
			message += '✗ <?php echo lang('source_path_invalid'); ?>\n';
		}
		
		if (response.destination_writable) {
			message += '✓ <?php echo lang('destination_writable'); ?>\n';
		} else {
			message += '✗ <?php echo lang('destination_not_writable'); ?>\n';
		}
		
		alert(message);
	}, 'json').fail(function() {
		alert('<?php echo lang('validation_failed'); ?>');
	});
}

function previewStructure() {
	const sourcePath = $('input[name="source_path"]').val();
	if (!sourcePath) {
		alert('<?php echo lang('please_enter_source_path'); ?>');
		return;
	}

	$('#structure-preview').html('<i class="fa fa-spinner fa-spin"></i> <?php echo lang('loading'); ?>...');

	$.post('<?php echo site_url('admin/copy_menue/preview_structure'); ?>', {
		source_path: sourcePath
	}, function(response) {
		if (response.error) {
			$('#structure-preview').html('<div class="alert alert-danger">' + response.error + '</div>');
		} else {
			$('#structure-preview').html(buildStructureTree(response));
		}
	}, 'json').fail(function() {
		$('#structure-preview').html('<div class="alert alert-danger"><?php echo lang('preview_failed'); ?></div>');
	});
}

function buildStructureTree(structure, level = 0) {
	let html = '<ul class="list-unstyled" style="margin-left: ' + (level * 20) + 'px;">';
	structure.forEach(function(item) {
		const icon = item.type === 'directory' ? '<i class="fa fa-folder text-warning"></i>' : '<i class="fa fa-file text-info"></i>';
		html += '<li style="padding: 2px 0;">' + icon + ' ' + item.name + '</li>';
		
		if (item.children && item.children.length > 0) {
			html += buildStructureTree(item.children, level + 1);
		}
	});
	html += '</ul>';
	return html;
}
</script>

<?php $this->load->view('template/footer'); ?>
