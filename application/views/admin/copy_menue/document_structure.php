<?php $this->load->view('template/header'); ?>

<div class="content-wrapper">
	<section class="content-header">
		<h1>
			<?php echo lang('copy_document_structure'); ?>
			<small><?php echo lang('copy_document_structure_desc'); ?></small>
		</h1>
		<ol class="breadcrumb">
			<li><a href="<?php echo site_url('admin'); ?>"><i class="fa fa-dashboard"></i> <?php echo lang('home'); ?></a></li>
			<li><a href="<?php echo site_url('admin/copy_menue'); ?>"><?php echo lang('copy_structure'); ?></a></li>
			<li class="active"><?php echo lang('copy_document_structure'); ?></li>
		</ol>
	</section>

	<section class="content">
		<!-- Flash messages -->
		<?php if ($this->session->flashdata('success')): ?>
			<div class="alert alert-success alert-dismissible">
				<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
				<i class="icon fa fa-check"></i> <?php echo $this->session->flashdata('success'); ?>
			</div>
		<?php endif; ?>

		<?php if ($this->session->flashdata('error')): ?>
			<div class="alert alert-danger alert-dismissible">
				<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
				<i class="icon fa fa-ban"></i> <?php echo $this->session->flashdata('error'); ?>
			</div>
		<?php endif; ?>

		<div class="row">
			<div class="col-md-8">
				<div class="box box-primary">
					<div class="box-header with-border">
						<h3 class="box-title"><i class="fa fa-copy"></i> <?php echo lang('copy_document_structure'); ?></h3>
					</div>
					
					<?php echo form_open('admin/copy_menue/copy_document_structure', ['class' => 'form-horizontal']); ?>
					<div class="box-body">
						<div class="form-group">
							<label class="col-sm-3 control-label"><?php echo lang('source_menu'); ?> <span class="text-red">*</span></label>
							<div class="col-sm-9">
								<select name="source_menu_id" class="form-control select2" required>
									<option value=""><?php echo lang('select_source_menu'); ?></option>
									<?php foreach($menus as $menu): ?>
										<?php if($menu->type == 'folder' || $menu->type == 'menu'): ?>
											<option value="<?php echo BIN_TO_UUID($menu->menu_id); ?>" <?php echo set_select('source_menu_id', BIN_TO_UUID($menu->menu_id)); ?>>
												<?php echo str_repeat('&nbsp;&nbsp;', $menu->level ?? 0) . $menu->name; ?>
												<?php if(isset($menu->document_count)): ?>
													(<?php echo $menu->document_count; ?> <?php echo lang('documents'); ?>)
												<?php endif; ?>
											</option>
										<?php endif; ?>
									<?php endforeach; ?>
								</select>
								<?php echo form_error('source_menu_id', '<div class="text-red">', '</div>'); ?>
								<span class="help-block"><?php echo lang('select_source_menu_help'); ?></span>
							</div>
						</div>

						<div class="form-group">
							<label class="col-sm-3 control-label"><?php echo lang('destination_name'); ?> <span class="text-red">*</span></label>
							<div class="col-sm-9">
								<?php echo form_input([
									'name' => 'destination_name',
									'class' => 'form-control',
									'placeholder' => lang('destination_name_placeholder'),
									'value' => set_value('destination_name'),
									'required' => 'required'
								]); ?>
								<?php echo form_error('destination_name', '<div class="text-red">', '</div>'); ?>
								<span class="help-block"><?php echo lang('destination_name_help'); ?></span>
							</div>
						</div>

						<div class="form-group">
							<label class="col-sm-3 control-label"><?php echo lang('copy_options'); ?></label>
							<div class="col-sm-9">
								<div class="checkbox">
									<label>
										<?php echo form_checkbox('copy_files', '1', set_checkbox('copy_files', '1')); ?>
										<?php echo lang('copy_physical_files'); ?>
									</label>
									<span class="help-block"><?php echo lang('copy_physical_files_help'); ?></span>
								</div>
							</div>
						</div>

						<div class="form-group">
							<div class="col-sm-offset-3 col-sm-9">
								<div class="callout callout-info">
									<h4><i class="icon fa fa-info"></i> <?php echo lang('note'); ?></h4>
									<p><?php echo lang('copy_document_structure_note'); ?></p>
									<ul>
										<li><?php echo lang('copy_note_1'); ?></li>
										<li><?php echo lang('copy_note_2'); ?></li>
										<li><?php echo lang('copy_note_3'); ?></li>
										<li><?php echo lang('copy_note_4'); ?></li>
									</ul>
								</div>
							</div>
						</div>
					</div>
					
					<div class="box-footer">
						<div class="col-sm-offset-3 col-sm-9">
							<button type="submit" class="btn btn-primary">
								<i class="fa fa-copy"></i> <?php echo lang('copy_structure'); ?>
							</button>
							<a href="<?php echo site_url('admin/copy_menue'); ?>" class="btn btn-default">
								<i class="fa fa-arrow-left"></i> <?php echo lang('back'); ?>
							</a>
						</div>
					</div>
					<?php echo form_close(); ?>
				</div>
			</div>

			<div class="col-md-4">
				<!-- Structure Preview -->
				<div class="box box-info">
					<div class="box-header with-border">
						<h3 class="box-title"><i class="fa fa-tree"></i> <?php echo lang('structure_preview'); ?></h3>
					</div>
					<div class="box-body">
						<div id="menu-preview">
							<p class="text-muted"><?php echo lang('select_menu_to_preview'); ?></p>
						</div>
					</div>
				</div>

				<!-- Statistics -->
				<div class="box box-success">
					<div class="box-header with-border">
						<h3 class="box-title"><i class="fa fa-bar-chart"></i> <?php echo lang('statistics'); ?></h3>
					</div>
					<div class="box-body">
						<div id="menu-stats">
							<p class="text-muted"><?php echo lang('select_menu_for_stats'); ?></p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
</div>

<script>
$(document).ready(function() {
	// Initialize Select2
	$('.select2').select2({
		placeholder: '<?php echo lang('select_source_menu'); ?>',
		allowClear: true
	});

	// Preview on menu selection change
	$('select[name="source_menu_id"]').on('change', function() {
		const menuId = $(this).val();
		if (menuId) {
			previewMenuStructure(menuId);
			getMenuStatistics(menuId);
		} else {
			$('#menu-preview').html('<p class="text-muted"><?php echo lang('select_menu_to_preview'); ?></p>');
			$('#menu-stats').html('<p class="text-muted"><?php echo lang('select_menu_for_stats'); ?></p>');
		}
	});
});

function previewMenuStructure(menuId) {
	$('#menu-preview').html('<i class="fa fa-spinner fa-spin"></i> <?php echo lang('loading'); ?>...');

	$.post('<?php echo site_url('admin/copy_menue/preview_menu_structure'); ?>', {
		menu_id: menuId
	}, function(response) {
		if (response.error) {
			$('#menu-preview').html('<div class="alert alert-danger">' + response.error + '</div>');
		} else {
			$('#menu-preview').html(buildMenuTree(response));
		}
	}, 'json').fail(function() {
		$('#menu-preview').html('<div class="alert alert-danger"><?php echo lang('preview_failed'); ?></div>');
	});
}

function getMenuStatistics(menuId) {
	$('#menu-stats').html('<i class="fa fa-spinner fa-spin"></i> <?php echo lang('loading'); ?>...');

	$.post('<?php echo site_url('admin/copy_menue/get_menu_statistics'); ?>', {
		menu_id: menuId
	}, function(response) {
		if (response.error) {
			$('#menu-stats').html('<div class="alert alert-danger">' + response.error + '</div>');
		} else {
			let html = '<dl class="dl-horizontal">';
			html += '<dt><?php echo lang('folders'); ?>:</dt><dd>' + response.folders + '</dd>';
			html += '<dt><?php echo lang('documents'); ?>:</dt><dd>' + response.documents + '</dd>';
			html += '<dt><?php echo lang('attachments'); ?>:</dt><dd>' + response.attachments + '</dd>';
			html += '<dt><?php echo lang('total_size'); ?>:</dt><dd>' + formatFileSize(response.total_size) + '</dd>';
			html += '</dl>';
			$('#menu-stats').html(html);
		}
	}, 'json').fail(function() {
		$('#menu-stats').html('<div class="alert alert-danger"><?php echo lang('stats_failed'); ?></div>');
	});
}

function buildMenuTree(structure, level = 0) {
	let html = '<ul class="list-unstyled" style="margin-left: ' + (level * 15) + 'px;">';
	structure.forEach(function(item) {
		const icon = item.type === 'folder' ? '<i class="fa fa-folder text-warning"></i>' : '<i class="fa fa-file-text text-info"></i>';
		html += '<li style="padding: 2px 0;">' + icon + ' ' + item.name;
		if (item.document_count && item.document_count > 0) {
			html += ' <span class="badge bg-blue">' + item.document_count + '</span>';
		}
		html += '</li>';
		
		if (item.children && item.children.length > 0) {
			html += buildMenuTree(item.children, level + 1);
		}
	});
	html += '</ul>';
	return html;
}

function formatFileSize(bytes) {
	if (bytes === 0) return '0 Bytes';
	const k = 1024;
	const sizes = ['Bytes', 'KB', 'MB', 'GB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
</script>

<?php $this->load->view('template/footer'); ?>
