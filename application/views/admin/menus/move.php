<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="float-right btn-group">
				<?php
					echo icon_anchor('admin/menus/view', array($folder_id, $sub_folder_id), '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default'
						)
					);
				?>
			</div>
			<h1>
				<?php echo lang('menus_menus'); ?>
				<small><?php echo lang('move'); ?></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('move'); ?> <?php echo html_escape($menu->name); ?></h3>
						</div>
						<div class="box-body">
							<p><?php echo lang('groups_group_desc'); ?></p>
							<?php 
								echo validation_errors();
								echo form_open(NULL,array(
									'id' => 'form-company-group',
									'autocomplete' => 'off'
								));
							?>
							<div class="form-group">
								<?php echo form_label(lang('folder_folders'),'move_menu'); ?>
								<ul class="move list-unstyled">
									<?php
									foreach($move['menu'][0] as $menu_id => $folder)
									{
										if($folder->menu_id != $menu->menu_id)
										{
											echo '
											<li>
												<div class="form-check">
													<label>
														<h4>' . form_radio('move_menu', $folder->menu_id, set_radio('move_menu', $folder->menu_id, $folder->menu_id === $menu->parent_id)) . html_escape($folder->name) . '</h4>
													</label>
												</div>
												<ul>
											';													
										}
										else
										{
											echo '<li><h4>' . html_escape($folder->name) . '</h4><ul>';
										}
										
										if( isset($move['menu'][$menu_id]) )
										{
											foreach($move['menu'][$menu_id] as $parent)
											{
												$parent->name = preg_replace('/^[0-9\.\s]+/u', '',$parent->name);
												// In case it's a folder
												if($parent->type == 1)
												{
													if($menu->type != 1)
													{
														echo '
														<li>
															<div class="form-check">
																<label>
																	<strong>' . form_radio('move_menu', $parent->menu_id, set_radio('move_menu', $parent->menu_id, $parent->menu_id === $menu->parent_id)) . html_escape($parent->name) . '</strong>
																</label>
															</div>
														';														
													}
													else
													{
														echo '<li><strong>' . html_escape($parent->name) . '</strong>';
													}

													// Echo $parent
													if(	isset($move['menu'][$parent->menu_id]) )
													{
														echo '<ul>';
														foreach($move['menu'][$parent->menu_id] as $child)
														{
															echo '<li> - <i>' . html_escape($child->name) . '</i></li>';
														}
														echo '</ul>';
													}
													echo '</li>';
													// Close $parent
													
												}
												else
												{
														echo '<li><strong>' . html_escape($parent->name) . '</strong>';
													// Echo $parent, that's not a folder
												}
											}
										}
										echo '</ul></li>';
									}
									?>
									<?php if( $menu->type == 1 ): ?>
									<li>
										<div class="form-check">
											<label>
												<h4><?php echo form_radio('move_menu', '00000000-0000-0000-0000-000000000000', set_radio('move_menu', '00000000-0000-0000-0000-000000000000')) . 'Ny mapp (går inte att ångra)</h4>' ;?>
											</label>
										</div>
									</li>
									<?php endif; ?>
								</ul>
							</div>
							<?php
							echo form_button(array(
								'type' => 'submit',
								'form' => 'form-company-group',
								'class' => 'btn btn-primary',
								'title' => lang('save'),
								'content' => lang('move')
							));
							?>
							<?php echo form_close(); ?>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');