<?php if( $this->auth_god ): ?>
	<li class="treeview">
		<a href="#">
			<span class="float-left-container">
				<i class="fa fa-angle-right"></i>
			</span>
			<span>FLEX</span>
		</a>
		<ul class="treeview-menu">
			<li class=""><a href="<?php echo site_url('admin/companies/company'); ?>">
				<span class="float-left-container"></span>Företag</a>
			</li>
			<li class=""><a href="<?php echo site_url('admin/companies/qualityassurance'); ?>">
				<span class="float-left-container"></span>Kvalitetssäkring</a>
			</li>
			<li class=""><a href="<?php echo site_url('admin/memberships'); ?>"><span>
				<span class="float-left-container"></span>Medlemskap</span></a>
			</li>
			<li class=""><a href="<?php echo site_url('admin/databases'); ?>"><span>
				<span class="float-left-container"></span>Databaser</span></a>
			</li>
		</ul>
	</li>
<?php endif; ?>
<li class="treeview">
	<a href="#">
		<span class="float-left-container">
			<i class="fa fa-angle-right"></i>
		</span>
		<span>Användarhantering</span>
	</a>
	<ul class="treeview-menu">
		<li class=""><a href="<?php echo site_url('admin/users'); ?>">
			<span class="float-left-container"></span>Användare</a>
		</li>
		<li class=""><a href="<?php echo site_url('admin/groups'); ?>">
			<span class="float-left-container"></span>Grupper</a>
		</li>
		<li class=""><a href="<?php echo site_url('admin/users/belongings'); ?>">
			<span class="float-left-container"></span>Grupp- &amp; avdelningstillhörighet </a>
		</li>
	</ul>
</li>
<li class="treeview">
	<a href="#">
		<span class="float-left-container">
			<i class="fa fa-angle-right"></i>
		</span>
		<span>Filhantering</span>
	</a>
	<ul class="treeview-menu">
		<li class=""><a href="<?php echo site_url('admin/documents'); ?>">
			<span class="float-left-container"></span>Dokument</a>
		</li>
		<li class=""><a href="<?php echo site_url('admin/documents/categories'); ?>">
			<span class="float-left-container"></span>Dokumentkategorier</a>
		</li>
		<li class=""><a href="<?php echo site_url('admin/documents/owner'); ?>">
			<span class="float-left-container"></span>Dokumentförfattare</a>
		</li>
		<li class=""><a href="<?php echo site_url('admin/documents/owner_individual'); ?>">
			<span class="float-left-container"></span>Dokumentförfattare (enstaka filer)</a>
		</li>
		<li class=""><a href="<?php echo site_url('admin/documents/move'); ?>">
			<span class="float-left-container"></span>Flytta/Kopiera dokument</a>
		</li>
	</ul>
</li>
<li class="treeview">
	<a href="#">
		<span class="float-left-container">
			<i class="fa fa-angle-right"></i>
		</span>
		<span>Utbildning</span>
	</a>
	<ul class="treeview-menu">
		<li class=""><a href="<?php echo site_url('admin/education'); ?>">
			<span class="float-left-container"></span>Grupper</a>
		</li>
		<li class=""><a href="<?php echo site_url('admin/education/users'); ?>">
			<span class="float-left-container"></span>Användare</a>
		</li>
	</ul>
</li>
<li class="treeview">
	<a href="#">
		<span class="float-left-container">
			<i class="fa fa-angle-right"></i>
		</span>
		<span>Menyhantering</span>
	</a>
	<ul class="treeview-menu">
		<li class=""><a href="<?php echo site_url('admin/menus'); ?>">
			<span class="float-left-container"></span>Menyer</a>
		</li>
		<li class=""><a href="<?php echo site_url('admin/menus/users'); ?>">
			<span class="float-left-container"></span>Dokumentägare</a>
		</li>
	</ul>
</li>
<li><a href="<?php echo site_url('admin/pages'); ?>">
	<span class="float-left-container"><i class="fa fa-circle"></i></span><span>Sidor</span></a>
</li>
<li class="treeview">
	<a href="#">
		<span class="float-left-container">
			<i class="fa fa-angle-right"></i>
		</span>
		<span>Inlägg</span>
	</a>
	<ul class="treeview-menu">
		<li class=""><a href="<?php echo site_url('admin/posts'); ?>">
			<span class="float-left-container"></span>Alla inlägg</a>
		</li>
		<li class=""><a href="<?php echo site_url('admin/posts/create'); ?>">
			<span class="float-left-container"></span>Skapa nytt</a>
		</li>
		<li class=""><a href="<?php echo site_url('admin/posts/display/categories'); ?>">
			<span class="float-left-container"></span>Kategorier</a>
		</li>
	</ul>
</li>
<li><a href="<?php echo site_url('admin/forms'); ?>">
	<span class="float-left-container"><i class="fa fa-circle"></i></span><span>Checklista</span></a>
</li>
<?php if ( $this->has_tasks ): ?>
<li class="treeview">
	<a href="#">
		<span class="float-left-container">
			<i class="fa fa-angle-right"></i>
		</span>
		<span><?php echo lang('tasks_task'); ?></span>
	</a>
	<ul class="treeview-menu">
		<li class=""><a href="<?php echo site_url('admin/tasks/types'); ?>">
			<span class="float-left-container"></span><?php echo lang('tasks_types'); ?></a>
		</li>
		<li class=""><a href="<?php echo site_url('admin/tasks/metric_types'); ?>">
			<span class="float-left-container"></span><?php echo lang('tasks_metric_types'); ?></a>
		</li>
	</ul>
</li>
<?php endif; ?>
<li class="treeview">
	<a href="#">
		<span class="float-left-container">
			<i class="fa fa-angle-right"></i>
		</span>
		<span>Avvikelse</span>
	</a>
	<ul class="treeview-menu">
		<li class=""><a href="<?php echo site_url('admin/deviation/fields'); ?>">
			<span class="float-left-container"></span>Fälthantering</a>
		</li>
		<li class=""><a href="<?php echo site_url('admin/deviation/fields/sort'); ?>">
			<span class="float-left-container"></span>Sortera fält</a>
		</li>
	</ul>
</li>
<?php if (CI_ONLY_OFFICE): ?>
	<li><a href="<?php echo site_url('admin/template'); ?>">
		<span class="float-left-container"><i class="fa fa-circle"></i></span><span>Mallar</span></a>
	</li>
<?php endif; ?>
<li><a href="<?php echo site_url('admin/appearance'); ?>">
	<span class="float-left-container"><i class="fa fa-circle"></i></span><span>Utseende</span></a>
</li>
<li><a href="<?php echo site_url('admin/documents/orna_analys'); ?>">
	<span class="float-left-container"><i class="fa fa-circle"></i></span><span>Orna Analys</span></a>
</li>
