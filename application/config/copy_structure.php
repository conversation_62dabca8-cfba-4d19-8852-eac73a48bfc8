<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
|--------------------------------------------------------------------------
| Copy Structure Configuration
|--------------------------------------------------------------------------
|
| This file contains configuration settings for the Copy Structure feature
|
*/

/*
|--------------------------------------------------------------------------
| Maximum Directory Depth
|--------------------------------------------------------------------------
|
| Maximum depth to traverse when previewing directory structure
| Higher values may cause performance issues with deep directory trees
|
*/
$config['copy_structure_max_depth'] = 5;

/*
|--------------------------------------------------------------------------
| Maximum Files to Preview
|--------------------------------------------------------------------------
|
| Maximum number of files to show in structure preview
| Prevents memory issues with directories containing many files
|
*/
$config['copy_structure_max_preview_files'] = 1000;

/*
|--------------------------------------------------------------------------
| Allowed Base Paths
|--------------------------------------------------------------------------
|
| Array of base paths that are allowed for copy operations
| This helps prevent unauthorized access to system directories
|
*/
$config['copy_structure_allowed_paths'] = [
    APPPATH,
    FCPATH,
    APPPATH . '../uploads/',
    APPPATH . '../temp/',
];

/*
|--------------------------------------------------------------------------
| Excluded Directories
|--------------------------------------------------------------------------
|
| Directories that should be excluded from copy operations
| These are typically system or sensitive directories
|
*/
$config['copy_structure_excluded_dirs'] = [
    '.git',
    '.svn',
    '.hg',
    'node_modules',
    '.DS_Store',
    'Thumbs.db',
    '__pycache__',
    '.cache',
    'logs',
    'cache'
];

/*
|--------------------------------------------------------------------------
| Excluded File Extensions
|--------------------------------------------------------------------------
|
| File extensions that should be excluded from copy operations
| These are typically temporary or system files
|
*/
$config['copy_structure_excluded_extensions'] = [
    'tmp',
    'temp',
    'log',
    'cache',
    'lock',
    'pid'
];

/*
|--------------------------------------------------------------------------
| Default Permissions
|--------------------------------------------------------------------------
|
| Default permissions for copied directories and files
|
*/
$config['copy_structure_default_dir_permissions'] = 0755;
$config['copy_structure_default_file_permissions'] = 0644;

/*
|--------------------------------------------------------------------------
| Memory Limit
|--------------------------------------------------------------------------
|
| Memory limit for copy operations (in MB)
| Set to 0 to use system default
|
*/
$config['copy_structure_memory_limit'] = 256;

/*
|--------------------------------------------------------------------------
| Time Limit
|--------------------------------------------------------------------------
|
| Maximum execution time for copy operations (in seconds)
| Set to 0 for no limit (not recommended for web requests)
|
*/
$config['copy_structure_time_limit'] = 300;

/*
|--------------------------------------------------------------------------
| Enable Logging
|--------------------------------------------------------------------------
|
| Whether to log copy operations
|
*/
$config['copy_structure_enable_logging'] = TRUE;

/*
|--------------------------------------------------------------------------
| Log File Path
|--------------------------------------------------------------------------
|
| Path to the log file for copy operations
|
*/
$config['copy_structure_log_file'] = APPPATH . 'logs/copy_structure.log';
