<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Remove index.php from URLs
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ index.php/$1 [QSA,L]
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Frame-Options DENY
    Header always set X-Content-Type-Options nosniff
</IfModule>

# Set environment variables
SetEnv CI_DB_HOSTNAME localhost
SetEnv CI_DB_USERNAME admin
SetEnv CI_DB_PASSWORD 123
SetEnv CI_DB_DATABASE kvalprak
SetEnv CI_BASE_URL http://localhost/
SetEnv CI_BASE_PATH /var/www/html/kvalprak/
SetEnv CI_PROGRAM_NAME "KvalPrak"
SetEnv CI_PROGRAM_DESC "Quality Practice Application"
SetEnv CI_PROGRAM_DESC_SHORT "KvalPrak"
SetEnv CI_ENV development
SetEnv DOC_SERV_SITE_URL “http://localhost/”
SetEnv DOC_SERV_JWT_SECRET “”
SetEnv CI_ONLY_OFFICE “true”





