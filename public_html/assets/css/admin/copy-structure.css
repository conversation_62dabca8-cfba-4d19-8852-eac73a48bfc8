/* Copy Structure Admin Styles */
.copy-structure-container {
    padding: 20px;
}

.path-input-group {
    margin-bottom: 15px;
}

.path-input-group .input-group {
    width: 100%;
}

.path-input-group .form-control {
    border-radius: 4px 0 0 4px;
}

.path-input-group .input-group-btn .btn {
    border-radius: 0 4px 4px 0;
    border-left: none;
}

.validation-panel,
.preview-panel {
    margin-top: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.validation-panel .panel-header,
.preview-panel .panel-header {
    background-color: #f5f5f5;
    padding: 10px 15px;
    border-bottom: 1px solid #ddd;
    border-radius: 4px 4px 0 0;
}

.validation-panel .panel-body,
.preview-panel .panel-body {
    padding: 15px;
}

.validation-results {
    display: flex;
    justify-content: space-between;
}

.validation-item {
    flex: 1;
    margin: 0 10px;
}

.validation-item h5 {
    color: #333;
    margin-bottom: 10px;
    font-weight: bold;
}

.validation-status {
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.validation-status.valid {
    background-color: #d4edda;
    color: #155724;
}

.validation-status.invalid {
    background-color: #f8d7da;
    color: #721c24;
}

.directory-modal .modal-dialog {
    max-width: 800px;
}

.directory-tree {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 4px;
}

.directory-item {
    display: block;
    padding: 8px 12px;
    color: #333;
    text-decoration: none;
    border-radius: 3px;
    margin: 2px 0;
    transition: background-color 0.2s;
}

.directory-item:hover {
    background-color: #f5f5f5;
    text-decoration: none;
    color: #333;
}

.directory-item.selected {
    background-color: #337ab7;
    color: white;
}

.directory-item.selected:hover {
    background-color: #286090;
    color: white;
}

.directory-item i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

.tree-structure {
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.tree-structure ul {
    list-style: none;
    padding-left: 20px;
    margin: 0;
}

.tree-structure > ul {
    padding-left: 0;
}

.tree-structure li {
    margin: 5px 0;
    position: relative;
}

.tree-structure li:before {
    content: '';
    position: absolute;
    left: -15px;
    top: 12px;
    width: 10px;
    height: 1px;
    background-color: #ccc;
}

.tree-structure li:after {
    content: '';
    position: absolute;
    left: -15px;
    top: 0;
    width: 1px;
    height: 12px;
    background-color: #ccc;
}

.tree-structure li:last-child:after {
    height: 12px;
}

.tree-structure i.fa-folder {
    color: #f39c12;
}

.tree-structure i.fa-file {
    color: #3498db;
}

.advanced-options {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    margin: 20px 0;
}

.advanced-options h4 {
    margin-top: 0;
    color: #495057;
}

.advanced-options .checkbox {
    margin-bottom: 10px;
}

.advanced-options .checkbox label {
    font-weight: normal;
    color: #6c757d;
}

.confirm-section {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 15px;
    margin: 20px 0;
}

.confirm-section .checkbox label {
    font-weight: bold;
    color: #856404;
}

.btn-copy-structure {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
    font-weight: bold;
    padding: 10px 20px;
}

.btn-copy-structure:hover {
    background-color: #218838;
    border-color: #1e7e34;
    color: white;
}

.btn-copy-structure:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
}

.progress-container {
    margin: 20px 0;
    display: none;
}

.progress {
    height: 25px;
    margin-bottom: 10px;
}

.progress-bar {
    line-height: 25px;
    font-weight: bold;
}

.copy-status {
    text-align: center;
    font-weight: bold;
    margin: 10px 0;
}

.copy-status.success {
    color: #28a745;
}

.copy-status.error {
    color: #dc3545;
}

.copy-status.processing {
    color: #007bff;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .validation-results {
        flex-direction: column;
    }
    
    .validation-item {
        margin: 10px 0;
    }
    
    .directory-modal .modal-dialog {
        max-width: 95%;
        margin: 10px auto;
    }
    
    .tree-structure {
        max-height: 300px;
    }
}

/* Loading spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0,0,0,.3);
    border-radius: 50%;
    border-top-color: #007bff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 10px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Alert improvements */
.alert-copy-structure {
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
}

.alert-copy-structure.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.alert-copy-structure.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.alert-copy-structure.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-copy-structure.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}
