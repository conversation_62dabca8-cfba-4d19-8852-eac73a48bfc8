/**
 * Copy Structure Admin JavaScript
 * Handles the copy structure functionality in the admin panel
 */

$(document).ready(function() {
    var currentTarget = '';
    var selectedPath = '';
    
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // Browse source directory
    $('#btn-browse-source').click(function() {
        currentTarget = 'source';
        $('#select-directory').text($('#select-directory').data('source-text') || 'Select Source');
        $('#directory-modal').modal('show');
        loadDirectories();
    });
    
    // Browse destination directory
    $('#btn-browse-destination').click(function() {
        currentTarget = 'destination';
        $('#select-directory').text($('#select-directory').data('destination-text') || 'Select Destination');
        $('#directory-modal').modal('show');
        loadDirectories();
    });
    
    // Validate paths
    $('#btn-validate').click(function() {
        var sourcePath = $('#source_path').val().trim();
        var destinationPath = $('#destination_path').val().trim();
        
        if (!sourcePath || !destinationPath) {
            showAlert('error', 'Please enter both source and destination paths.');
            return;
        }
        
        // Show loading
        $(this).prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Validating...');
        
        $.ajax({
            url: base_url + 'admin/copy_menue/validate_path',
            type: 'POST',
            data: {
                source_path: sourcePath,
                destination_path: destinationPath
            },
            dataType: 'json',
            success: function(data) {
                displayValidationResults(data);
            },
            error: function() {
                showAlert('error', 'Error occurred while validating paths.');
            },
            complete: function() {
                $('#btn-validate').prop('disabled', false).html('<i class="fa fa-check"></i> Validate Paths');
            }
        });
    });
    
    // Preview structure
    $('#btn-preview').click(function() {
        var sourcePath = $('#source_path').val().trim();
        
        if (!sourcePath) {
            showAlert('error', 'Please enter source path first.');
            return;
        }
        
        // Show loading
        $(this).prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Loading Preview...');
        
        $.ajax({
            url: base_url + 'admin/copy_menue/preview_structure',
            type: 'POST',
            data: {
                source_path: sourcePath
            },
            dataType: 'json',
            success: function(data) {
                displayPreviewResults(data);
            },
            error: function() {
                showAlert('error', 'Error occurred while loading preview.');
            },
            complete: function() {
                $('#btn-preview').prop('disabled', false).html('<i class="fa fa-eye"></i> Preview Structure');
            }
        });
    });
    
    // Form submission with confirmation
    $('#form-copy-structure').submit(function(e) {
        if (!$('#confirm_copy').is(':checked')) {
            e.preventDefault();
            showAlert('warning', 'Please confirm that you want to proceed with the copy operation.');
            return false;
        }
        
        var overwrite = $('#overwrite_existing').is(':checked');
        if (overwrite) {
            if (!confirm('Warning: This will overwrite existing files. Are you sure you want to continue?')) {
                e.preventDefault();
                return false;
            }
        }
        
        // Show progress
        showProgress();
    });
    
    // Load directories for modal
    function loadDirectories(basePath) {
        $('#directory-tree').html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading directories...</div>');
        
        $.ajax({
            url: base_url + 'admin/copy_menue/get_directories',
            type: 'POST',
            data: {
                base_path: basePath || ''
            },
            dataType: 'json',
            success: function(data) {
                var html = '<ul class="list-unstyled">';
                if (data && data.length > 0) {
                    $.each(data, function(index, dir) {
                        html += '<li>';
                        html += '<a href="#" class="directory-item" data-path="' + escapeHtml(dir.path) + '">';
                        html += '<i class="fa fa-folder"></i> ' + escapeHtml(dir.name);
                        html += '</a>';
                        html += '</li>';
                    });
                } else {
                    html += '<li class="text-muted">No directories found</li>';
                }
                html += '</ul>';
                $('#directory-tree').html(html);
            },
            error: function() {
                $('#directory-tree').html('<div class="alert alert-danger">Error loading directories</div>');
            }
        });
    }
    
    // Select directory
    $(document).on('click', '.directory-item', function(e) {
        e.preventDefault();
        $('.directory-item').removeClass('selected');
        $(this).addClass('selected');
        selectedPath = $(this).data('path');
    });
    
    // Confirm directory selection
    $('#select-directory').click(function() {
        if (selectedPath) {
            if (currentTarget === 'source') {
                $('#source_path').val(selectedPath);
            } else {
                $('#destination_path').val(selectedPath);
            }
            $('#directory-modal').modal('hide');
            selectedPath = '';
        } else {
            showAlert('warning', 'Please select a directory first.');
        }
    });
    
    // Display validation results
    function displayValidationResults(data) {
        var html = '<div class="validation-results">';
        
        // Source validation
        html += '<div class="validation-item">';
        html += '<h5>Source Path</h5>';
        html += '<p><strong>Valid:</strong> ';
        html += '<span class="validation-status ' + (data.source_valid ? 'valid' : 'invalid') + '">';
        html += data.source_valid ? 'Yes' : 'No';
        html += '</span></p>';
        html += '<p><strong>Exists:</strong> ';
        html += '<span class="validation-status ' + (data.source_exists ? 'valid' : 'invalid') + '">';
        html += data.source_exists ? 'Yes' : 'No';
        html += '</span></p>';
        html += '</div>';
        
        // Destination validation
        html += '<div class="validation-item">';
        html += '<h5>Destination Path</h5>';
        html += '<p><strong>Writable:</strong> ';
        html += '<span class="validation-status ' + (data.destination_writable ? 'valid' : 'invalid') + '">';
        html += data.destination_writable ? 'Yes' : 'No';
        html += '</span></p>';
        html += '</div>';
        
        html += '</div>';
        
        $('#validation-results').html(html);
        $('#validation-panel').show();
        
        // Scroll to results
        $('html, body').animate({
            scrollTop: $('#validation-panel').offset().top - 100
        }, 500);
    }
    
    // Display preview results
    function displayPreviewResults(data) {
        if (data.error) {
            $('#preview-results').html('<div class="alert alert-danger">' + escapeHtml(data.error) + '</div>');
        } else {
            var html = '<div class="tree-structure">';
            html += buildTreeHTML(data, 0);
            html += '</div>';
            $('#preview-results').html(html);
        }
        $('#preview-panel').show();
        
        // Scroll to results
        $('html, body').animate({
            scrollTop: $('#preview-panel').offset().top - 100
        }, 500);
    }
    
    // Build tree HTML recursively
    function buildTreeHTML(items, level) {
        if (!items || items.length === 0) {
            return '<p class="text-muted">No items found</p>';
        }
        
        var html = '<ul>';
        $.each(items, function(index, item) {
            html += '<li>';
            if (item.type === 'directory') {
                html += '<i class="fa fa-folder"></i> <strong>' + escapeHtml(item.name) + '</strong>';
                if (item.children && item.children.length > 0) {
                    html += buildTreeHTML(item.children, level + 1);
                }
            } else {
                html += '<i class="fa fa-file"></i> ' + escapeHtml(item.name);
            }
            html += '</li>';
        });
        html += '</ul>';
        return html;
    }
    
    // Show progress during copy operation
    function showProgress() {
        var progressHtml = '<div class="progress-container">';
        progressHtml += '<div class="progress">';
        progressHtml += '<div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%">0%</div>';
        progressHtml += '</div>';
        progressHtml += '<div class="copy-status processing">Copying files...</div>';
        progressHtml += '</div>';
        
        $('.box-body').append(progressHtml);
        $('.progress-container').show();
        
        // Disable form elements
        $('#form-copy-structure input, #form-copy-structure button').prop('disabled', true);
    }
    
    // Show alert messages
    function showAlert(type, message) {
        var alertClass = 'alert-' + type;
        var iconClass = type === 'error' ? 'fa-ban' : (type === 'warning' ? 'fa-warning' : 'fa-info');
        
        var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible alert-copy-structure">';
        alertHtml += '<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>';
        alertHtml += '<i class="icon fa ' + iconClass + '"></i> ' + escapeHtml(message);
        alertHtml += '</div>';
        
        $('.content .row .col-md-12').prepend(alertHtml);
        
        // Auto-dismiss after 5 seconds
        setTimeout(function() {
            $('.alert-copy-structure').fadeOut();
        }, 5000);
    }
    
    // Escape HTML to prevent XSS
    function escapeHtml(text) {
        var map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }
    
    // Set base URL for AJAX calls
    var base_url = $('base').attr('href') || window.location.origin + '/';
    if (!base_url.endsWith('/')) {
        base_url += '/';
    }
});
