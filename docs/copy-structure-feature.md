# Copy Structure Feature Documentation

## Overview
The Copy Structure feature allows administrators to copy complete directory structures with all files and subdirectories while preserving the original hierarchy and optionally maintaining file permissions and timestamps.

## Features
- **Complete Directory Copying**: Recursively copies all files and subdirectories
- **Path Validation**: Validates source and destination paths before copying
- **Structure Preview**: Shows a preview of the directory structure before copying
- **Advanced Options**: 
  - Preserve file permissions
  - Preserve timestamps
  - Exclude hidden files
  - Overwrite existing files option
- **Interactive Directory Browser**: Browse and select directories through a modal interface
- **Progress Tracking**: Visual feedback during the copy operation
- **Error Handling**: Comprehensive error handling with user-friendly messages

## Files Created/Modified

### Controller
- `application/controllers/admin/Copy_menue.php` - Main controller handling copy operations

### Views
- `application/views/admin/copy_menue/index.php` - Main interface for copy structure functionality

### Language Files
- `application/language/swedish/intranet/copy_menue_lang.php` - Swedish language translations

### Assets
- `public_html/assets/css/admin/copy-structure.css` - Styling for the copy structure interface
- `public_html/assets/js/admin/copy-structure.js` - JavaScript functionality for interactive features

### Configuration
- `application/config/routes.php` - Added routes for copy structure functionality
- `application/views/template/sidebar-admin.php` - Added menu item in admin sidebar

### Documentation
- `docs/copy-structure-feature.md` - This documentation file

## Usage

### Accessing the Feature
1. Log in as an administrator
2. Navigate to Admin Panel
3. Go to "Filhantering" (File Management) section
4. Click on "Kopiera struktur" (Copy Structure)

### Using the Copy Structure Tool
1. **Enter Source Path**: Specify the directory you want to copy from
2. **Enter Destination Path**: Specify where you want to copy the structure to
3. **Browse Directories**: Use the browse buttons to select directories interactively
4. **Validate Paths**: Click "Validate Paths" to check if paths are valid and accessible
5. **Preview Structure**: Click "Preview Structure" to see what will be copied
6. **Configure Advanced Options**:
   - Check "Preserve Permissions" to maintain file permissions
   - Check "Preserve Timestamps" to keep original file dates
   - Check "Exclude Hidden Files" to skip hidden files and directories
   - Check "Overwrite Existing" to replace existing files (use with caution)
7. **Confirm Operation**: Check the confirmation checkbox
8. **Execute Copy**: Click "Copy Now" to start the operation

### API Endpoints
- `GET /admin/copy_menue` - Display the copy structure interface
- `POST /admin/copy_menue/copy_structure` - Execute the copy operation
- `POST /admin/copy_menue/get_directories` - Get list of available directories
- `POST /admin/copy_menue/preview_structure` - Preview directory structure
- `POST /admin/copy_menue/validate_path` - Validate source and destination paths

## Technical Details

### Controller Methods
- `index()` - Display the main interface
- `copy_structure()` - Handle form submission and execute copy operation
- `_copy_directory_recursive()` - Private method for recursive directory copying
- `get_directories()` - Return JSON list of directories for browser modal
- `preview_structure()` - Return JSON structure preview
- `_get_directory_structure()` - Private method to build directory structure array
- `validate_path()` - Validate paths and return status

### Security Features
- Admin authentication required
- Path validation to prevent directory traversal attacks
- Confirmation required before executing copy operations
- Error handling for permission issues

### Error Handling
- Source directory existence validation
- Destination directory write permission checks
- File copy operation error handling
- User-friendly error messages in Swedish

## Language Support
All text strings are externalized to language files supporting internationalization. Currently implemented in Swedish with English fallbacks.

## Browser Compatibility
- Modern browsers with JavaScript enabled
- jQuery dependency
- Bootstrap 3.x styling compatibility
- Font Awesome icons

## Permissions Required
- Administrator role in the system
- Read access to source directories
- Write access to destination directories
- Appropriate file system permissions

## Limitations
- Large directory structures may take significant time to copy
- Memory usage scales with directory size
- Network timeouts may occur for very large operations
- File system permissions must allow the operation

## Future Enhancements
- Progress bar with real-time updates
- Background processing for large operations
- Copy operation logging
- Selective file/directory copying
- Compression options for large transfers
- Resume capability for interrupted operations
