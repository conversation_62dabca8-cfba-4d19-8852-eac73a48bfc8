# API Authentication Guide

## Overview
The API uses JWT (JSON Web Token) authentication. You need to login first to get a token, then use that token for subsequent API calls.

## Step 1: Login to get JWT Token

**Endpoint:** `POST /api/auth/login`

**Request Headers:**
```
Content-Type: application/json
```

**Request Body:**
```json
{
    "login_email": "<EMAIL>",
    "login_password": "123"
}
```

**Response (Success):**
```json
{
    "refresh_token": "abc123...",
    "refresh_token_expiry": "2024-01-15 12:00:00",
    "name": "User Name",
    "email": "<EMAIL>",
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

## Step 2: Use JWT Token for API Calls

For all subsequent API calls, include the JWT token in the Authorization header:

**Request Headers:**
```
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
Content-Type: application/json
```

## Example API Call

**Endpoint:** `POST /api/deviation/form/1`

**Request Headers:**
```
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json
```

**Request Body:**
```json
{
    "field_data": "your_data_here"
}
```

## Postman Setup

### 1. Login Request:
- Method: POST
- URL: `http://localhost/kvalprak/api/auth/login`
- Headers: `Content-Type: application/json`
- Body (raw JSON):
```json
{
    "login_email": "<EMAIL>",
    "login_password": "123"
}
```

### 2. Copy the token from response and use it in other requests:
- Method: POST
- URL: `http://localhost/kvalprak/api/deviation/form/1`
- Headers: 
  - `Authorization: Bearer YOUR_TOKEN_HERE`
  - `Content-Type: application/json`
- Body: Your API data

## Token Refresh

If your token expires, you can refresh it using:

**Endpoint:** `POST /api/auth/refresh`

**Request Body:**
```json
{
    "refresh_token": "your_refresh_token_here",
    "login_email": "<EMAIL>"
}
```

## Common Errors

### 403 Forbidden
- **Cause:** Missing or invalid JWT token
- **Solution:** Make sure you include `Authorization: Bearer TOKEN` header

### 401 Unauthorized
- **Cause:** Invalid credentials or expired token
- **Solution:** Login again to get a new token

### Authorization header missing
- **Cause:** No Authorization header in request
- **Solution:** Add `Authorization: Bearer TOKEN` to headers

## Quick Fix for Your Current Issue

1. First, make a POST request to `/api/auth/login` with your credentials
2. Copy the `token` value from the response
3. In your original API request, add header: `Authorization: Bearer COPIED_TOKEN`
4. Your request should now work

## Environment Variables for Postman

You can set up Postman environment variables:
- `base_url`: `http://localhost/kvalprak`
- `jwt_token`: (set this after login)

Then use:
- URL: `{{base_url}}/api/auth/login`
- Authorization: `Bearer {{jwt_token}}`
